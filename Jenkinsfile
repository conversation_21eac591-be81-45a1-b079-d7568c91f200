pipeline {
  agent any
  triggers {
    githubPush()
  }
  options {
    buildDiscarder(logRotator(numToKeepStr: "10"))
  }
  stages {
    stage('Build') {
      when {
        branch 'main'
      }
      agent {
        docker {
          image 'node:22.11.0-slim'
          args '--user 0:0'
        }
      }
      steps {
        slackSend(message: "BECOMAP.COM Frontend Build started ${env.JOB_NAME} ${env.BUILD_NUMBER} (<${env.BUILD_URL}|Open>)")
        sh "npm install -g npm@10.9.0"
        sh "npm ci"
        sh "npm install next@14.2.13"
        sh "chmod -R 777 ./"
        sh "npm run  build"
        sh "apt update"
        sh "apt-get install -y awscli"
        withCredentials([
          [
            $class: 'AmazonWebServicesCredentialsBinding',
            accessKeyVariable: 'AWS_ACCESS_KEY_ID',
            secretKeyVariable: 'AWS_SECRET_ACCESS_KEY',
            credentialsId: 'BECOMAP_COM_FRONTEND'
          ]
        ]) {
          sh 'aws s3 sync  ./build s3://${BECOMAP_WEBSITE_FRONTEND_S3}'
          sh 'aws configure set preview.cloudfront true'
          sh 'aws cloudfront create-invalidation --distribution-id ${BECOMAP_WEBSITE_FRONTEND_CDN} --paths "/*"'
          sh 'rm -rf ./build .next'
          slackSend(color: '#00FF00', message: "Build deployed successfully")
        }
      }
      post {
        success {
          slackSend(color: '#00FF00', message: "SUCCESSFUL: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}] ${env.BUILD_LOG}' (${env.BUILD_URL})")
        }
        failure {
          slackSend(color: '#FF0000', message: "FAILED: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]' (${env.BUILD_URL})")
        }
      }
    }
  }
}

