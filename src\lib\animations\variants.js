// Animation variants for Framer Motion
// These provide consistent, reusable animations across the website

// Fade animations
export const fadeIn = {
  hidden: { 
    opacity: 0 
  },
  visible: { 
    opacity: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const fadeInUp = {
  hidden: { 
    opacity: 0, 
    y: 30 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const fadeInDown = {
  hidden: { 
    opacity: 0, 
    y: -30 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const fadeInLeft = {
  hidden: { 
    opacity: 0, 
    x: -30 
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const fadeInRight = {
  hidden: { 
    opacity: 0, 
    x: 30 
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

// Scale animations
export const scaleIn = {
  hidden: { 
    opacity: 0, 
    scale: 0.8 
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
};

export const scaleOnHover = {
  rest: { 
    scale: 1 
  },
  hover: { 
    scale: 1.05,
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  }
};

// Button animations
export const buttonHover = {
  rest: { 
    scale: 1,
    boxShadow: "0px 10px 30px rgba(69, 69, 69, 0.09)"
  },
  hover: { 
    scale: 1.02,
    boxShadow: "0px 15px 40px rgba(69, 69, 69, 0.15)",
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  },
  tap: { 
    scale: 0.98,
    transition: {
      duration: 0.1,
      ease: "easeInOut"
    }
  }
};

export const buttonPrimary = {
  rest: { 
    scale: 1,
    backgroundColor: "#34A853",
    boxShadow: "0px 10px 30px rgba(52, 168, 83, 0.2)"
  },
  hover: { 
    scale: 1.02,
    backgroundColor: "#2d8f47",
    boxShadow: "0px 15px 40px rgba(52, 168, 83, 0.3)",
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  },
  tap: { 
    scale: 0.98,
    transition: {
      duration: 0.1,
      ease: "easeInOut"
    }
  }
};

export const buttonSecondary = {
  rest: { 
    scale: 1,
    backgroundColor: "#FFB82D",
    boxShadow: "0px 10px 30px rgba(255, 184, 45, 0.2)"
  },
  hover: { 
    scale: 1.02,
    backgroundColor: "#e6a429",
    boxShadow: "0px 15px 40px rgba(255, 184, 45, 0.3)",
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  },
  tap: { 
    scale: 0.98,
    transition: {
      duration: 0.1,
      ease: "easeInOut"
    }
  }
};

// Container animations for staggered children
export const staggerContainer = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

export const staggerItem = {
  hidden: { 
    opacity: 0, 
    y: 20 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
};

// Navigation animations
export const navSlideDown = {
  hidden: { 
    opacity: 0, 
    y: -20,
    height: 0
  },
  visible: { 
    opacity: 1, 
    y: 0,
    height: "auto",
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  },
  exit: { 
    opacity: 0, 
    y: -20,
    height: 0,
    transition: {
      duration: 0.3,
      ease: "easeIn"
    }
  }
};

export const mobileMenuSlide = {
  hidden: { 
    x: "100%",
    opacity: 0
  },
  visible: { 
    x: 0,
    opacity: 1,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  },
  exit: { 
    x: "100%",
    opacity: 0,
    transition: {
      duration: 0.3,
      ease: "easeIn"
    }
  }
};

// Page transition animations
export const pageTransition = {
  hidden: { 
    opacity: 0,
    y: 20
  },
  visible: { 
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  },
  exit: { 
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.4,
      ease: "easeIn"
    }
  }
};

// Loading animations
export const loadingSpinner = {
  animate: {
    rotate: 360,
    transition: {
      duration: 1,
      repeat: Infinity,
      ease: "linear"
    }
  }
};

export const loadingPulse = {
  animate: {
    scale: [1, 1.1, 1],
    opacity: [0.7, 1, 0.7],
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

// Form animations
export const formFieldFocus = {
  rest: { 
    borderColor: "rgba(0, 0, 0, 0.78)",
    boxShadow: "none"
  },
  focus: { 
    borderColor: "#34A853",
    boxShadow: "0 0 0 3px rgba(52, 168, 83, 0.1)",
    transition: {
      duration: 0.2,
      ease: "easeOut"
    }
  }
};

export const formError = {
  hidden: { 
    opacity: 0, 
    y: -10,
    height: 0
  },
  visible: { 
    opacity: 1, 
    y: 0,
    height: "auto",
    transition: {
      duration: 0.3,
      ease: "easeOut"
    }
  }
};

// Scroll-triggered animations
export const scrollReveal = {
  hidden: { 
    opacity: 0, 
    y: 50 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
};

export const scrollRevealLeft = {
  hidden: { 
    opacity: 0, 
    x: -50 
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
};

export const scrollRevealRight = {
  hidden: { 
    opacity: 0, 
    x: 50 
  },
  visible: { 
    opacity: 1, 
    x: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
};
