// Enhanced animations and transitions
// Respects user's motion preferences

// Base animation settings
:root {
  --animation-duration-fast: 0.2s;
  --animation-duration-normal: 0.4s;
  --animation-duration-slow: 0.6s;
  --animation-ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --animation-ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  --animation-ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
  --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

// Respect user's motion preferences
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

// Enhanced button animations
.btn {
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
  transform: translateZ(0); // Enable hardware acceleration
  will-change: transform, box-shadow;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0px 15px 40px rgba(69, 69, 69, 0.15);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
    transition-duration: var(--animation-duration-fast);
  }
  
  &:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
}

.border-btn {
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
  transform: translateZ(0);
  will-change: transform, box-shadow, background-color;
  
  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0px 10px 25px rgba(52, 168, 83, 0.2);
    background-color: darken($primary-color, 5%);
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
    transition-duration: var(--animation-duration-fast);
  }
}

// Enhanced form animations
.form-control,
.search-input {
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
  
  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba(52, 168, 83, 0.1);
    transform: translateY(-1px);
  }
  
  &.is-invalid {
    animation: shake 0.5s ease-in-out;
  }
}

// Shake animation for form errors
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

// Enhanced navigation animations
.header-wrap {
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
  
  &.sticky {
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  }
}

.nav-link {
  position: relative;
  transition: color var(--animation-duration-normal) var(--animation-ease-out);
  
  &::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: $primary-color;
    transition: all var(--animation-duration-normal) var(--animation-ease-out);
    transform: translateX(-50%);
  }
  
  &:hover::after,
  &.active::after {
    width: 100%;
  }
}

// Dropdown animations
.dropdown-menu {
  opacity: 0;
  transform: translateY(-10px);
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
  pointer-events: none;
  
  &.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
  }
}

// Card hover animations
.card,
.service-card,
.blog-card {
  transition: all var(--animation-duration-normal) var(--animation-ease-out);
  transform: translateZ(0);
  will-change: transform, box-shadow;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
  }
}

// Image hover effects
.image-hover {
  overflow: hidden;
  
  img {
    transition: transform var(--animation-duration-slow) var(--animation-ease-out);
    
    &:hover {
      transform: scale(1.05);
    }
  }
}

// Loading animations
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.loading-dots {
  display: inline-flex;
  gap: 4px;
  
  &::after {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: currentColor;
    animation: loading-dots 1.4s infinite ease-in-out both;
  }
  
  &::before {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: currentColor;
    animation: loading-dots 1.4s infinite ease-in-out both;
    animation-delay: -0.16s;
  }
}

@keyframes loading-dots {
  0%, 80%, 100% { 
    transform: scale(0);
    opacity: 0.5;
  } 
  40% { 
    transform: scale(1);
    opacity: 1;
  }
}

// Scroll animations
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all var(--animation-duration-slow) var(--animation-ease-out);
  
  &.revealed {
    opacity: 1;
    transform: translateY(0);
  }
}

.scroll-reveal-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: all var(--animation-duration-slow) var(--animation-ease-out);
  
  &.revealed {
    opacity: 1;
    transform: translateX(0);
  }
}

.scroll-reveal-right {
  opacity: 0;
  transform: translateX(30px);
  transition: all var(--animation-duration-slow) var(--animation-ease-out);
  
  &.revealed {
    opacity: 1;
    transform: translateX(0);
  }
}

// Stagger animations
.stagger-container {
  .stagger-item {
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--animation-duration-slow) var(--animation-ease-out);
    
    @for $i from 1 through 10 {
      &:nth-child(#{$i}) {
        transition-delay: #{$i * 0.1}s;
      }
    }
    
    &.revealed {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

// Focus animations
.focus-ring {
  &:focus-visible {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
    border-radius: 4px;
  }
}

// Pulse animation
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 168, 83, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(52, 168, 83, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 168, 83, 0);
  }
}

// Bounce animation
.bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -10px, 0);
  }
  70% {
    transform: translate3d(0, -5px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

// Fade animations
.fade-in {
  opacity: 0;
  animation: fadeIn var(--animation-duration-slow) var(--animation-ease-out) forwards;
}

.fade-in-up {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp var(--animation-duration-slow) var(--animation-ease-out) forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Utility classes
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.hardware-acceleration {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}
