// Animation library exports
export * from './variants';
export * from './hooks';
export * from './components';
export * from './accessibility';

// Utility functions for animations
export const getReducedMotionVariant = (normalVariant, reducedVariant = {}) => {
  if (typeof window !== 'undefined') {
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    return prefersReducedMotion ? reducedVariant : normalVariant;
  }
  return normalVariant;
};

// Animation configuration
export const animationConfig = {
  // Default durations
  duration: {
    instant: 0,
    fast: 0.2,
    normal: 0.4,
    slow: 0.6,
    slower: 0.8,
    slowest: 1.2
  },

  // Default easing
  ease: {
    linear: "linear",
    easeOut: "easeOut",
    easeIn: "easeIn",
    easeInOut: "easeInOut",
    bounce: [0.68, -0.55, 0.265, 1.55],
    elastic: [0.25, 0.46, 0.45, 0.94],
    spring: { type: "spring", stiffness: 300, damping: 30 }
  },

  // Default delays
  delay: {
    none: 0,
    short: 0.1,
    medium: 0.2,
    long: 0.4,
    longer: 0.6
  },

  // Stagger settings
  stagger: {
    fast: 0.05,
    normal: 0.1,
    slow: 0.2,
    slower: 0.3
  },

  // Intersection observer settings
  intersection: {
    threshold: 0.1,
    rootMargin: "-50px",
    triggerOnce: true
  },

  // Performance settings
  performance: {
    enableHardwareAcceleration: true,
    enableWillChange: true,
    enableBackfaceVisibility: true
  }
};

// Global animation utilities
export const globalAnimationUtils = {
  /**
   * Initialize global animation settings
   */
  init: () => {
    if (typeof window === 'undefined') return;

    // Add CSS custom properties for animation settings
    const root = document.documentElement;
    root.style.setProperty('--animation-duration-fast', `${animationConfig.duration.fast}s`);
    root.style.setProperty('--animation-duration-normal', `${animationConfig.duration.normal}s`);
    root.style.setProperty('--animation-duration-slow', `${animationConfig.duration.slow}s`);

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      root.style.setProperty('--animation-duration-fast', '0.01ms');
      root.style.setProperty('--animation-duration-normal', '0.01ms');
      root.style.setProperty('--animation-duration-slow', '0.01ms');
    }
  },

  /**
   * Add performance optimizations to an element
   */
  optimizeElement: (element) => {
    if (!element || !animationConfig.performance.enableHardwareAcceleration) return;

    element.style.transform = 'translateZ(0)';
    if (animationConfig.performance.enableBackfaceVisibility) {
      element.style.backfaceVisibility = 'hidden';
    }
    if (animationConfig.performance.enableWillChange) {
      element.style.willChange = 'transform, opacity';
    }
  },

  /**
   * Clean up performance optimizations
   */
  cleanupElement: (element) => {
    if (!element) return;

    element.style.transform = '';
    element.style.backfaceVisibility = '';
    element.style.willChange = '';
  }
};
