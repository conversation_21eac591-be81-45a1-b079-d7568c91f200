# Becomap - Indoor Navigation Website

A modern Next.js website for Becomap, providing indoor navigation, wayfinding, and mapping solutions. This project helps improve visitor experience and staff management through innovative indoor mapping and navigation technologies.

## 🚀 Project Overview

Becomap offers comprehensive indoor navigation solutions including:
- **Indoor Mapping**: Detailed floor plans and interactive maps
- **Wayfinding**: Turn-by-turn navigation for indoor spaces
- **Asset Tracking**: Real-time location tracking for equipment and personnel
- **Analytics**: Visitor flow analysis and space utilization insights
- **Proximity Services**: Location-based notifications and services

## 📁 Directory Structure

The project follows Next.js 14 best practices with a clean, organized structure:

```
src/
├── app/                          # Next.js App Router
│   ├── (routes)/                 # Route groups for pages
│   │   ├── blog/                 # Blog pages and dynamic routes
│   │   ├── contact/              # Contact page
│   │   ├── company/              # Company information
│   │   ├── career/               # Career opportunities
│   │   ├── casestudy/            # Case studies
│   │   ├── industry/             # Industry-specific solutions
│   │   ├── solutions/            # Product solutions
│   │   └── thankyou/             # Thank you page
│   ├── home/                     # Home page components
│   ├── globals.scss              # Global styles
│   ├── layout.js                 # Root layout component
│   └── page.js                   # Home page entry point
├── components/                   # Reusable components
│   ├── layout/                   # Layout components
│   │   ├── Header/               # Site header with navigation
│   │   ├── Footer/               # Site footer
│   │   └── index.js              # Layout exports
│   ├── ui/                       # Basic UI components
│   │   ├── WhatsAppIcon/         # WhatsApp floating icon
│   │   ├── GetADemoModal/        # Demo request modal
│   │   ├── TalkToModal/          # Contact modal
│   │   ├── Loader/               # Loading component
│   │   └── index.js              # UI exports
│   ├── sections/                 # Page sections
│   │   ├── home/                 # Home page sections
│   │   ├── blog/                 # Blog page sections
│   │   ├── contact/              # Contact page sections
│   │   ├── company/              # Company page sections
│   │   ├── career/               # Career page sections
│   │   ├── casestudy/            # Case study sections
│   │   ├── industry/             # Industry sections
│   │   ├── solutions/            # Solutions sections
│   │   ├── TalkToOurTeam/        # Shared CTA section
│   │   └── index.js              # Section exports
│   ├── forms/                    # Form components (future)
│   └── icons/                    # Icon components
├── lib/                          # Utilities and configurations
│   ├── actions/                  # Server actions and API calls
│   │   └── index.js              # API functions
│   ├── utils/                    # Utility functions
│   │   ├── contactBtnAnimation.js
│   │   ├── slugify.js
│   │   ├── truncate.js
│   │   ├── validatePhoneNumber.js
│   │   └── index.js              # Utility exports
│   ├── constants/                # Configuration constants
│   │   └── index.js              # API endpoints, site config
│   ├── hooks/                    # Custom React hooks (future)
│   └── index.js                  # Library exports
├── styles/                       # Styling files
│   ├── components/               # Component-specific styles
│   ├── pages/                    # Page-specific styles
│   ├── utils/                    # Style utilities and variables
│   ├── _app.scss                 # Global app styles
│   └── style.scss                # Main style imports
└── assets/                       # Static assets
    ├── fonts/                    # Custom font files
    └── images/                   # Image assets
```

## 🛠️ Installation and Setup

### Prerequisites
- **Node.js**: 22.11.0 or higher
- **npm**: 10.9.0 or higher (or yarn/pnpm)

### Getting Started

1. **Clone the repository**
   ```bash
   git clone https://github.com/sayone-tech/becomap-website.git
   cd becomap-website
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000) to see the application.

## 💻 Development Guidelines

### Component Organization

#### Adding New Components

**Layout Components** (`src/components/layout/`)
- Header, Footer, and other layout-related components
- Export from `src/components/layout/index.js`

**UI Components** (`src/components/ui/`)
- Reusable UI elements like buttons, modals, icons
- Export from `src/components/ui/index.js`

**Section Components** (`src/components/sections/`)
- Page-specific sections organized by page
- Export from `src/components/sections/index.js`

#### Import Patterns

**Recommended Import Patterns:**
```javascript
// Layout components
import { Header, Footer } from '@/components/layout';

// UI components
import { WhatsAppIcon, GetADemoModal } from '@/components/ui';

// Section components
import { TalkToOurTeam, HomeBanner } from '@/components/sections';

// Utilities and actions
import { formSubmit, getCarouselBlog } from '@/lib/actions';
import { slugify, truncate } from '@/lib/utils';
import { SITE_CONFIG, API_ENDPOINTS } from '@/lib/constants';
```

**Avoid These Patterns:**
```javascript
// ❌ Don't use relative imports for shared components
import Header from '../../../components/layout/Header';

// ❌ Don't import from deep nested paths
import { formSubmit } from '@/app/action/action';
```

### File Naming Conventions

- **Components**: PascalCase directories with index.js files
- **Utilities**: camelCase file names
- **Constants**: UPPER_SNAKE_CASE for constants
- **Styles**: kebab-case for CSS classes

### Adding New Features

1. **New Page**: Create in `src/app/` following App Router conventions
2. **New Component**: Place in appropriate `src/components/` subdirectory
3. **New Utility**: Add to `src/lib/utils/` and export from index.js
4. **New API Function**: Add to `src/lib/actions/` and export from index.js
5. **New Constants**: Add to `src/lib/constants/index.js`

## 🏗️ Build and Deployment

### Development Build
```bash
npm run dev
```

### Production Build
```bash
npm run build
```
Build output is saved to the `build` folder.

### Start Production Server
```bash
npm start
```

### Linting
```bash
npm run lint
```

### Project Dependencies
- **Next.js**: 14.2.13
- **React**: 18.x
- **Bootstrap**: 5.3.3
- **Sass**: For styling
- **Axios**: For API calls

## 📈 Recent Changes (Code Restructuring)

The project has been recently restructured to follow Next.js 14 best practices:

### Key Improvements

✅ **Organized Component Structure**
- Centralized all components in `src/components/` with logical grouping
- Created clear separation between layout, UI, and section components

✅ **Centralized Library Functions**
- Moved API functions to `src/lib/actions/`
- Organized utilities in `src/lib/utils/`
- Created constants file for configuration

✅ **Improved Asset Management**
- Relocated assets to `src/assets/`
- Updated font paths and import statements

✅ **Enhanced Code Quality**
- Fixed function naming and improved error messages
- Implemented clean import patterns
- Added proper TypeScript-ready structure

### Migration Benefits
- **Better Maintainability**: Easier to find and modify components
- **Improved Scalability**: Structure supports future growth
- **Enhanced Developer Experience**: Cleaner imports and organization
- **Consistent Conventions**: Following Next.js best practices

## 🤝 Contributing Guidelines

### Before Contributing
1. Ensure you understand the new directory structure
2. Follow the established import patterns
3. Use the appropriate component directories

### Code Standards
- Use functional components with hooks
- Follow the established naming conventions
- Add proper error handling for API calls
- Use TypeScript types when available

### Pull Request Process
1. Create a feature branch from `main`
2. Follow the component organization guidelines
3. Update documentation if needed
4. Ensure all builds pass successfully
5. Request review from team members

### Getting Help
- Check the `RESTRUCTURE_GUIDE.md` for detailed migration information
- Review existing components for patterns and conventions
- Ask team members for guidance on component placement

---

**Built with ❤️ by the Becomap team**